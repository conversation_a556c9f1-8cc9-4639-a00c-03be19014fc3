/**
 * @file position_control.c
 * @brief 步进电机位置记录和回归控制实现文件
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */

#include "position_control.h"
#include "uart_bsp.h"

/* 全局变量定义 */
Motor_Position_t current_position = {0, 0};
Motor_Position_t saved_position = {0, 0};
Position_Control_State_t pos_ctrl_state = POS_CTRL_IDLE;

/* 私有变量 */
static Emm_V5_Response_t x_response, y_response;
static int position_read_timeout = 0;

/**
 * @brief 位置控制初始化
 */
void Position_Control_Init(void)
{
    pos_ctrl_state = POS_CTRL_IDLE;
    current_position.x_position = 0;
    current_position.y_position = 0;
    
    // 尝试从FLASH读取保存的位置
    if (Flash_Read_Position(&saved_position) == 0) {
        my_printf(&huart1, "从FLASH读取保存位置: X=%d, Y=%d\r\n", 
                 saved_position.x_position, saved_position.y_position);
    } else {
        my_printf(&huart1, "FLASH中无有效位置数据\r\n");
        saved_position.x_position = 0;
        saved_position.y_position = 0;
    }
}

/**
 * @brief 读取电机当前绝对位置
 * @retval 0:成功 -1:失败
 */
int Read_Motor_Current_Position(void)
{
    // 发送读取X轴当前位置命令
    Emm_V5_Read_Sys_Params(&MOTOR_X_UART, MOTOR_X_ADDR, S_CPOS);
    HAL_Delay(100); // 等待响应
    
    // 发送读取Y轴当前位置命令  
    Emm_V5_Read_Sys_Params(&MOTOR_Y_UART, MOTOR_Y_ADDR, S_CPOS);
    HAL_Delay(100); // 等待响应
    
    // 注意：实际项目中需要解析UART接收到的响应数据
    // 这里简化处理，假设能正确获取到位置
    // 在实际应用中需要实现UART接收中断和数据解析
    
    my_printf(&huart1, "已发送位置读取命令\r\n");
    return 0;
}

/**
 * @brief 保存当前位置到FLASH
 * @retval 0:成功 -1:失败
 */
int Save_Current_Position_To_Flash(void)
{
    pos_ctrl_state = POS_CTRL_SAVING;
    
    // 先读取当前位置
    if (Read_Motor_Current_Position() != 0) {
        pos_ctrl_state = POS_CTRL_ERROR;
        my_printf(&huart1, "读取当前位置失败\r\n");
        return -1;
    }
    
    // 模拟获取到的位置数据(实际应用中从UART响应解析)
    // 这里使用示例值，实际需要从电机响应中获取
    current_position.x_position = 1000; // 示例值
    current_position.y_position = 2000; // 示例值
    
    // 保存到FLASH
    if (Flash_Save_Position(&current_position) == 0) {
        saved_position = current_position;
        pos_ctrl_state = POS_CTRL_IDLE;
        my_printf(&huart1, "位置已保存到FLASH: X=%d, Y=%d\r\n", 
                 current_position.x_position, current_position.y_position);
        return 0;
    } else {
        pos_ctrl_state = POS_CTRL_ERROR;
        my_printf(&huart1, "保存位置到FLASH失败\r\n");
        return -1;
    }
}

/**
 * @brief 回到保存的位置
 * @retval 0:成功 -1:失败
 */
int Return_To_Saved_Position(void)
{
    pos_ctrl_state = POS_CTRL_RETURNING;
    
    // 从FLASH读取保存的位置
    if (Flash_Read_Position(&saved_position) != 0) {
        pos_ctrl_state = POS_CTRL_ERROR;
        my_printf(&huart1, "FLASH中无有效位置数据\r\n");
        return -1;
    }
    
    my_printf(&huart1, "开始回到保存位置: X=%d, Y=%d\r\n", 
             saved_position.x_position, saved_position.y_position);
    
    // 使用绝对位置模式移动到保存的位置
    // X轴移动
    if (saved_position.x_position != 0) {
        uint8_t x_dir = (saved_position.x_position >= 0) ? 0 : 1;
        uint32_t x_clk = (saved_position.x_position >= 0) ? 
                        saved_position.x_position : -saved_position.x_position;
        
        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, MOTOR_MAX_SPEED, 
                          MOTOR_ACCEL, x_clk, true, MOTOR_SYNC_FLAG); // true=绝对位置
    }
    
    // Y轴移动
    if (saved_position.y_position != 0) {
        uint8_t y_dir = (saved_position.y_position >= 0) ? 0 : 1;
        uint32_t y_clk = (saved_position.y_position >= 0) ? 
                        saved_position.y_position : -saved_position.y_position;
        
        Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, MOTOR_MAX_SPEED, 
                          MOTOR_ACCEL, y_clk, true, MOTOR_SYNC_FLAG); // true=绝对位置
    }
    
    // 等待运动完成(根据位置估算时间)
    int x_abs = (saved_position.x_position >= 0) ? saved_position.x_position : -saved_position.x_position;
    int y_abs = (saved_position.y_position >= 0) ? saved_position.y_position : -saved_position.y_position;
    int max_pos = (x_abs > y_abs) ? x_abs : y_abs;
    int delay_time = (max_pos / 500) * 1000 + 2000; // 估算延时
    if (delay_time > 10000) delay_time = 10000; // 最大10秒
    
    HAL_Delay(delay_time);
    
    pos_ctrl_state = POS_CTRL_IDLE;
    my_printf(&huart1, "已回到保存位置\r\n");
    
    return 0;
}

/**
 * @brief 按键处理函数
 * @param key 按键值(1=KEY1, 2=KEY2)
 */
void Position_Control_Key_Handler(int key)
{
    if (pos_ctrl_state != POS_CTRL_IDLE) {
        my_printf(&huart1, "系统忙，请稍后再试\r\n");
        return;
    }
    
    switch (key) {
        case 1: // KEY1 - 读取并保存当前位置
            my_printf(&huart1, "KEY1按下 - 保存当前位置\r\n");
            Save_Current_Position_To_Flash();
            break;
            
        case 2: // KEY2 - 回到保存的位置
            my_printf(&huart1, "KEY2按下 - 回到保存位置\r\n");
            Return_To_Saved_Position();
            break;
            
        default:
            break;
    }
}

/**
 * @brief 位置控制处理函数(在主循环中调用)
 */
void Position_Control_Process(void)
{
    // 这里可以添加状态机处理逻辑
    // 例如超时处理、错误恢复等
    
    if (pos_ctrl_state == POS_CTRL_ERROR) {
        // 错误状态自动恢复
        static int error_count = 0;
        error_count++;
        if (error_count > 1000) { // 约1秒后恢复
            pos_ctrl_state = POS_CTRL_IDLE;
            error_count = 0;
            my_printf(&huart1, "错误状态已恢复\r\n");
        }
    }
}
