/**
 * @file flash_bsp.h
 * @brief FLASH存储操作BSP头文件
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */

#ifndef __FLASH_BSP_H__
#define __FLASH_BSP_H__

#include "bsp_system.h"

/* FLASH存储地址定义 - 使用最后一个扇区存储数据 */
#define FLASH_SECTOR_11         11                    // 扇区11
#define FLASH_POSITION_ADDR     0x080E0000           // 扇区11起始地址
#define FLASH_POSITION_SIZE     8                    // 存储8字节(X轴4字节+Y轴4字节)

/* 位置数据结构 */
typedef struct {
    int32_t x_position;  // X轴绝对位置
    int32_t y_position;  // Y轴绝对位置
} Motor_Position_t;

/* 函数声明 */
int Flash_Save_Position(Motor_Position_t *position);     // 保存位置到FLASH
int Flash_Read_Position(Motor_Position_t *position);     // 从FLASH读取位置
int Flash_Erase_Position(void);                         // 擦除位置数据

#endif /* __FLASH_BSP_H__ */
