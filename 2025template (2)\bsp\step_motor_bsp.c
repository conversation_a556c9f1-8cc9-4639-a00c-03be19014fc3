#include "step_motor_bsp.h"

/**
 * @brief �����ʼ������
 */
void Step_Motor_Init(void)
{
    /* ʹ��X���� */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* ʹ��Y���� */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* ��ʼֹͣ */
    Step_Motor_Stop();
}

/**
 * @brief ����XY�����ٶ�
 * @param x_percent X���ٶȰٷֱȣ���Χ-100��100
 * @param y_percent Y���ٶȰٷֱȣ���Χ-100��100
 */
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* ���ưٷֱȷ�Χ */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* ����X�᷽�� */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW���� */
    }
    else
    {
        x_dir = 1;              /* CCW���� */
        x_percent = -x_percent; /* ȡ����ֵ */
    }

    /* ����Y�᷽�� */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW���� */
    }
    else
    {
        y_dir = 1;              /* CCW���� */
        y_percent = -y_percent; /* ȡ����ֵ */
    }

    /* ����ʵ���ٶ�ֵ(�ٷֱ�ת��ΪRPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ����XY�����ƶ�һ�ξ��루ʹ��λ��ģʽ��
 * @param x_distance X���ƶ����루������������ֵΪCW���򣬸�ֵΪCCW����
 * @param y_distance Y���ƶ����루������������ֵΪCW���򣬸�ֵΪCCW����
 */
void Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance)
{
    uint8_t x_dir, y_dir;
    uint32_t x_clk, y_clk;
    uint16_t speed = MOTOR_MAX_SPEED;  /* ʹ������ٶȣ��������Ҫ���� */
    uint8_t acc = MOTOR_ACCEL;        /* ʹ��Ԥ������ٶ� */

    /* ����X�᷽��������� */
    if (x_distance >= 0)
    {
        x_dir = 0; /* CW���� */
        x_clk = (uint32_t)x_distance;
    }
    else
    {
        x_dir = 1; /* CCW���� */
        x_clk = (uint32_t)(-x_distance); /* ȡ����ֵ */
    }

    /* ����Y�᷽��������� */
    if (y_distance >= 0)
    {
        y_dir = 0; /* CW���� */
        y_clk = (uint32_t)y_distance;
    }
    else
    {
        y_dir = 1; /* CCW���� */
        y_clk = (uint32_t)(-y_distance); /* ȡ����ֵ */
    }

    /* ����X����������˶��������þ���ģʽ�� */
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, speed, acc, x_clk, false, MOTOR_SYNC_FLAG);

    /* ����Y����������˶��������þ���ģʽ�� */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, speed, acc, y_clk, false, MOTOR_SYNC_FLAG);
}


/**
 * @brief ֹͣ���е��
 */
void Step_Motor_Stop(void)
{
    /* ֹͣX���� */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* ֹͣY���� */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}

void step_motor_proc(void)
{

}



