#include "step_motor_bsp.h"
#include "uart_bsp.h"
#include "stm32f4xx_hal_flash.h"
#include "stm32f4xx_hal_flash_ex.h"

/**
 * @brief �����ʼ������
 */
void Step_Motor_Init(void)
{
    /* ʹ��X���� */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* ʹ��Y���� */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* ��ʼֹͣ */
    Step_Motor_Stop();
}

/**
 * @brief ����XY�����ٶ�
 * @param x_percent X���ٶȰٷֱȣ���Χ-100��100
 * @param y_percent Y���ٶȰٷֱȣ���Χ-100��100
 */
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* ���ưٷֱȷ�Χ */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* ����X�᷽�� */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW���� */
    }
    else
    {
        x_dir = 1;              /* CCW���� */
        x_percent = -x_percent; /* ȡ����ֵ */
    }

    /* ����Y�᷽�� */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW���� */
    }
    else
    {
        y_dir = 1;              /* CCW���� */
        y_percent = -y_percent; /* ȡ����ֵ */
    }

    /* ����ʵ���ٶ�ֵ(�ٷֱ�ת��ΪRPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ����XY�����ƶ�һ�ξ��루ʹ��λ��ģʽ��
 * @param x_distance X���ƶ����루������������ֵΪCW���򣬸�ֵΪCCW����
 * @param y_distance Y���ƶ����루������������ֵΪCW���򣬸�ֵΪCCW����
 */
void Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance)
{
    uint8_t x_dir, y_dir;
    uint32_t x_clk, y_clk;
    uint16_t speed = MOTOR_MAX_SPEED;  /* ʹ������ٶȣ��������Ҫ���� */
    uint8_t acc = MOTOR_ACCEL;        /* ʹ��Ԥ������ٶ� */

    /* ����X�᷽��������� */
    if (x_distance >= 0)
    {
        x_dir = 0; /* CW���� */
        x_clk = (uint32_t)x_distance;
    }
    else
    {
        x_dir = 1; /* CCW���� */
        x_clk = (uint32_t)(-x_distance); /* ȡ����ֵ */
    }

    /* ����Y�᷽��������� */
    if (y_distance >= 0)
    {
        y_dir = 0; /* CW���� */
        y_clk = (uint32_t)y_distance;
    }
    else
    {
        y_dir = 1; /* CCW���� */
        y_clk = (uint32_t)(-y_distance); /* ȡ����ֵ */
    }

    /* ����X����������˶��������þ���ģʽ�� */
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, speed, acc, x_clk, false, MOTOR_SYNC_FLAG);

    /* ����Y����������˶��������þ���ģʽ�� */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, speed, acc, y_clk, false, MOTOR_SYNC_FLAG);
}


/**
 * @brief ֹͣ���е��
 */
void Step_Motor_Stop(void)
{
    /* ֹͣX���� */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* ֹͣY���� */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}

void step_motor_proc(void)
{

}

/* 位置控制功能实现 */
#define FLASH_SECTOR_11         11                    // 扇区11
#define FLASH_POSITION_ADDR     0x080E0000           // 扇区11起始地址

/* 全局变量 */
Motor_Position_t current_position = {0, 0};
Motor_Position_t saved_position = {0, 0};
static int pos_ctrl_state = 0; // 0=空闲, 1=忙碌

/**
 * @brief 位置控制初始化
 */
void Position_Control_Init(void)
{
    pos_ctrl_state = 0;
    current_position.x_position = 0;
    current_position.y_position = 0;

    // 尝试从FLASH读取保存的位置
    saved_position.x_position = *(int*)FLASH_POSITION_ADDR;
    saved_position.y_position = *(int*)(FLASH_POSITION_ADDR + 4);

    // 检查数据有效性(FLASH擦除后为0xFFFFFFFF)
    if (saved_position.x_position == -1 && saved_position.y_position == -1) {
        saved_position.x_position = 0;
        saved_position.y_position = 0;
        my_printf(&huart1, "FLASH中无有效位置数据\r\n");
    } else {
        my_printf(&huart1, "从FLASH读取保存位置: X=%d, Y=%d\r\n",
                 saved_position.x_position, saved_position.y_position);
    }
}

/**
 * @brief 保存位置数据到FLASH
 */
static int Flash_Save_Position(Motor_Position_t *position)
{
    HAL_StatusTypeDef status;
    FLASH_EraseInitTypeDef erase_init;
    unsigned int sector_error = 0;

    if (position == NULL) return -1;

    // 解锁FLASH
    HAL_FLASH_Unlock();

    // 配置擦除参数
    erase_init.TypeErase = FLASH_TYPEERASE_SECTORS;
    erase_init.Sector = FLASH_SECTOR_11;
    erase_init.NbSectors = 1;
    erase_init.VoltageRange = FLASH_VOLTAGE_RANGE_3;

    // 擦除扇区
    status = HAL_FLASHEx_Erase(&erase_init, &sector_error);
    if (status != HAL_OK) {
        HAL_FLASH_Lock();
        return -1;
    }

    // 写入X轴位置(4字节)
    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, FLASH_POSITION_ADDR,
                              (unsigned int)position->x_position);
    if (status != HAL_OK) {
        HAL_FLASH_Lock();
        return -1;
    }

    // 写入Y轴位置(4字节)
    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, FLASH_POSITION_ADDR + 4,
                              (unsigned int)position->y_position);
    if (status != HAL_OK) {
        HAL_FLASH_Lock();
        return -1;
    }

    // 锁定FLASH
    HAL_FLASH_Lock();
    return 0;
}

/**
 * @brief 保存当前位置到FLASH
 */
int Save_Current_Position_To_Flash(void)
{
    if (pos_ctrl_state != 0) {
        my_printf(&huart1, "系统忙，请稍后再试\r\n");
        return -1;
    }

    pos_ctrl_state = 1;

    // 发送读取X轴当前位置命令
    Emm_V5_Read_Sys_Params(&MOTOR_X_UART, MOTOR_X_ADDR, S_CPOS);
    HAL_Delay(100);

    // 发送读取Y轴当前位置命令
    Emm_V5_Read_Sys_Params(&MOTOR_Y_UART, MOTOR_Y_ADDR, S_CPOS);
    HAL_Delay(100);

    // 模拟获取到的位置数据(实际应用中从UART响应解析)
    current_position.x_position = 1000; // 示例值，实际需要从电机响应中获取
    current_position.y_position = 2000; // 示例值，实际需要从电机响应中获取

    // 保存到FLASH
    if (Flash_Save_Position(&current_position) == 0) {
        saved_position = current_position;
        my_printf(&huart1, "位置已保存到FLASH: X=%d, Y=%d\r\n",
                 current_position.x_position, current_position.y_position);
        pos_ctrl_state = 0;
        return 0;
    } else {
        my_printf(&huart1, "保存位置到FLASH失败\r\n");
        pos_ctrl_state = 0;
        return -1;
    }
}

/**
 * @brief 回到保存的位置
 */
int Return_To_Saved_Position(void)
{
    if (pos_ctrl_state != 0) {
        my_printf(&huart1, "系统忙，请稍后再试\r\n");
        return -1;
    }

    pos_ctrl_state = 1;

    // 从FLASH读取保存的位置
    saved_position.x_position = *(int*)FLASH_POSITION_ADDR;
    saved_position.y_position = *(int*)(FLASH_POSITION_ADDR + 4);

    // 检查数据有效性
    if (saved_position.x_position == -1 && saved_position.y_position == -1) {
        my_printf(&huart1, "FLASH中无有效位置数据\r\n");
        pos_ctrl_state = 0;
        return -1;
    }

    my_printf(&huart1, "开始回到保存位置: X=%d, Y=%d\r\n",
             saved_position.x_position, saved_position.y_position);

    // 使用绝对位置模式移动到保存的位置
    if (saved_position.x_position != 0) {
        unsigned char x_dir = (saved_position.x_position >= 0) ? 0 : 1;
        unsigned int x_clk = (saved_position.x_position >= 0) ?
                        saved_position.x_position : -saved_position.x_position;

        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, MOTOR_MAX_SPEED,
                          MOTOR_ACCEL, x_clk, true, MOTOR_SYNC_FLAG);
    }

    if (saved_position.y_position != 0) {
        unsigned char y_dir = (saved_position.y_position >= 0) ? 0 : 1;
        unsigned int y_clk = (saved_position.y_position >= 0) ?
                        saved_position.y_position : -saved_position.y_position;

        Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, MOTOR_MAX_SPEED,
                          MOTOR_ACCEL, y_clk, true, MOTOR_SYNC_FLAG);
    }

    // 等待运动完成
    int x_abs = (saved_position.x_position >= 0) ? saved_position.x_position : -saved_position.x_position;
    int y_abs = (saved_position.y_position >= 0) ? saved_position.y_position : -saved_position.y_position;
    int max_pos = (x_abs > y_abs) ? x_abs : y_abs;
    int delay_time = (max_pos / 500) * 1000 + 2000;
    if (delay_time > 10000) delay_time = 10000;

    HAL_Delay(delay_time);

    my_printf(&huart1, "已回到保存位置\r\n");
    pos_ctrl_state = 0;
    return 0;
}

/**
 * @brief 按键处理函数
 */
void Position_Control_Key_Handler(int key)
{
    switch (key) {
        case 1: // KEY1 - 读取并保存当前位置
            my_printf(&huart1, "KEY1按下 - 保存当前位置\r\n");
            Save_Current_Position_To_Flash();
            break;

        case 2: // KEY2 - 回到保存的位置
            my_printf(&huart1, "KEY2按下 - 回到保存位置\r\n");
            Return_To_Saved_Position();
            break;

        default:
            break;
    }
}

/**
 * @brief 位置控制处理函数
 */
void Position_Control_Process(void)
{
    // 这里可以添加状态机处理逻辑
    // 例如超时处理、错误恢复等
}

/**
 * @brief 位置功能测试(可选调用)
 */
void Position_Function_Test(void)
{
    my_printf(&huart1, "=== 位置记录和回归功能测试 ===\r\n");

    // 等待系统稳定
    HAL_Delay(2000);

    // 模拟移动到某个位置
    my_printf(&huart1, "1. 移动电机到测试位置...\r\n");
    Step_Motor_Set_Pwm(1500, -1000);
    HAL_Delay(3000);
    Step_Motor_Stop();

    HAL_Delay(1000);

    // 模拟按下KEY1
    my_printf(&huart1, "2. 模拟按下KEY1 - 保存当前位置\r\n");
    Position_Control_Key_Handler(1);

    HAL_Delay(2000);

    // 移动到其他位置
    my_printf(&huart1, "3. 移动到其他位置...\r\n");
    Step_Motor_Set_Pwm(-800, 1200);
    HAL_Delay(3000);
    Step_Motor_Stop();

    HAL_Delay(1000);

    // 模拟按下KEY2
    my_printf(&huart1, "4. 模拟按下KEY2 - 回到保存位置\r\n");
    Position_Control_Key_Handler(2);

    my_printf(&huart1, "=== 测试完成 ===\r\n");
}



