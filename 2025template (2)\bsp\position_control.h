/**
 * @file position_control.h
 * @brief 步进电机位置记录和回归控制头文件
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */

#ifndef __POSITION_CONTROL_H__
#define __POSITION_CONTROL_H__

#include "bsp_system.h"
#include "flash_bsp.h"
#include "step_motor_bsp.h"
#include "Emm_V5.h"

/* 位置控制状态 */
typedef enum {
    POS_CTRL_IDLE = 0,      // 空闲状态
    POS_CTRL_READING,       // 正在读取位置
    POS_CTRL_SAVING,        // 正在保存位置
    POS_CTRL_RETURNING,     // 正在回到位置
    POS_CTRL_ERROR          // 错误状态
} Position_Control_State_t;

/* 全局变量声明 */
extern Motor_Position_t current_position;
extern Motor_Position_t saved_position;
extern Position_Control_State_t pos_ctrl_state;

/* 函数声明 */
void Position_Control_Init(void);                    // 位置控制初始化
void Position_Control_Process(void);                 // 位置控制处理函数
int Read_Motor_Current_Position(void);               // 读取电机当前绝对位置
int Save_Current_Position_To_Flash(void);            // 保存当前位置到FLASH
int Return_To_Saved_Position(void);                  // 回到保存的位置
void Position_Control_Key_Handler(int key);          // 按键处理函数

#endif /* __POSITION_CONTROL_H__ */
