/**
 * @file position_test_example.c
 * @brief 位置记录和回归功能测试示例
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */

#include "position_control.h"
#include "uart_bsp.h"

/**
 * @brief 位置功能测试示例
 * 
 * 使用方法：
 * 1. 在main函数中调用Position_Function_Test()
 * 2. 观察串口输出和电机动作
 */

/**
 * @brief 模拟按键测试
 * 用于测试位置记录和回归功能
 */
void Position_Function_Test(void)
{
    my_printf(&huart1, "=== 位置记录和回归功能测试 ===\r\n");
    
    // 等待系统稳定
    HAL_Delay(2000);
    
    // 模拟移动到某个位置
    my_printf(&huart1, "1. 移动电机到测试位置...\r\n");
    Step_Motor_Set_Pwm(1500, -1000); // 移动到测试位置
    HAL_Delay(3000); // 等待运动完成
    Step_Motor_Stop();
    
    HAL_Delay(1000);
    
    // 模拟按下KEY1 - 保存当前位置
    my_printf(&huart1, "2. 模拟按下KEY1 - 保存当前位置\r\n");
    Position_Control_Key_Handler(1);
    
    HAL_Delay(2000);
    
    // 移动到其他位置
    my_printf(&huart1, "3. 移动到其他位置...\r\n");
    Step_Motor_Set_Pwm(-800, 1200); // 移动到其他位置
    HAL_Delay(3000); // 等待运动完成
    Step_Motor_Stop();
    
    HAL_Delay(1000);
    
    // 模拟按下KEY2 - 回到保存位置
    my_printf(&huart1, "4. 模拟按下KEY2 - 回到保存位置\r\n");
    Position_Control_Key_Handler(2);
    
    HAL_Delay(1000);
    
    my_printf(&huart1, "=== 测试完成 ===\r\n");
}

/**
 * @brief FLASH存储测试
 */
void Flash_Storage_Test(void)
{
    Motor_Position_t test_pos = {2000, -1500};
    Motor_Position_t read_pos = {0, 0};
    
    my_printf(&huart1, "=== FLASH存储测试 ===\r\n");
    
    // 测试保存
    my_printf(&huart1, "保存测试位置: X=%d, Y=%d\r\n", test_pos.x_position, test_pos.y_position);
    if (Flash_Save_Position(&test_pos) == 0) {
        my_printf(&huart1, "保存成功\r\n");
    } else {
        my_printf(&huart1, "保存失败\r\n");
        return;
    }
    
    HAL_Delay(100);
    
    // 测试读取
    if (Flash_Read_Position(&read_pos) == 0) {
        my_printf(&huart1, "读取成功: X=%d, Y=%d\r\n", read_pos.x_position, read_pos.y_position);
        
        // 验证数据
        if (read_pos.x_position == test_pos.x_position && 
            read_pos.y_position == test_pos.y_position) {
            my_printf(&huart1, "数据验证成功\r\n");
        } else {
            my_printf(&huart1, "数据验证失败\r\n");
        }
    } else {
        my_printf(&huart1, "读取失败\r\n");
    }
    
    my_printf(&huart1, "=== FLASH测试完成 ===\r\n");
}

/**
 * @brief 按键响应测试
 * 在主循环中调用，测试按键响应
 */
void Key_Response_Test(void)
{
    static int test_count = 0;
    static int last_time = 0;
    
    // 每5秒输出一次提示信息
    if (HAL_GetTick() - last_time > 5000) {
        test_count++;
        my_printf(&huart1, "按键测试 %d: 请按KEY1保存位置，按KEY2回到位置\r\n", test_count);
        last_time = HAL_GetTick();
    }
}

/* 
使用示例：

方法1：在main函数初始化后调用自动测试
Position_Function_Test();

方法2：在main函数初始化后调用FLASH测试
Flash_Storage_Test();

方法3：在主循环中调用按键测试
while(1) {
    schedule_run();
    Position_Control_Process();
    Key_Response_Test();  // 添加这行
}

*/
