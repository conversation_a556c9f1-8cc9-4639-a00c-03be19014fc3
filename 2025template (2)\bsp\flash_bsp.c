/**
 * @file flash_bsp.c
 * @brief FLASH存储操作BSP实现文件
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */

#include "flash_bsp.h"
#include "stm32f4xx_hal_flash.h"
#include "stm32f4xx_hal_flash_ex.h"

/**
 * @brief 保存位置数据到FLASH
 * @param position 位置数据指针
 * @retval 0:成功 -1:失败
 */
int Flash_Save_Position(Motor_Position_t *position)
{
    HAL_StatusTypeDef status;
    FLASH_EraseInitTypeDef erase_init;
    uint32_t sector_error = 0;
    
    if (position == NULL) return -1;
    
    // 解锁FLASH
    HAL_FLASH_Unlock();
    
    // 配置擦除参数
    erase_init.TypeErase = FLASH_TYPEERASE_SECTORS;
    erase_init.Sector = FLASH_SECTOR_11;
    erase_init.NbSectors = 1;
    erase_init.VoltageRange = FLASH_VOLTAGE_RANGE_3;
    
    // 擦除扇区
    status = HAL_FLASHEx_Erase(&erase_init, &sector_error);
    if (status != HAL_OK) {
        HAL_FLASH_Lock();
        return -1;
    }
    
    // 写入X轴位置(4字节)
    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, FLASH_POSITION_ADDR, 
                              (uint32_t)position->x_position);
    if (status != HAL_OK) {
        HAL_FLASH_Lock();
        return -1;
    }
    
    // 写入Y轴位置(4字节)
    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, FLASH_POSITION_ADDR + 4, 
                              (uint32_t)position->y_position);
    if (status != HAL_OK) {
        HAL_FLASH_Lock();
        return -1;
    }
    
    // 锁定FLASH
    HAL_FLASH_Lock();
    
    return 0;
}

/**
 * @brief 从FLASH读取位置数据
 * @param position 位置数据指针
 * @retval 0:成功 -1:失败
 */
int Flash_Read_Position(Motor_Position_t *position)
{
    if (position == NULL) return -1;
    
    // 直接从FLASH地址读取数据
    position->x_position = *(int32_t*)FLASH_POSITION_ADDR;
    position->y_position = *(int32_t*)(FLASH_POSITION_ADDR + 4);
    
    // 检查数据有效性(FLASH擦除后为0xFFFFFFFF)
    if (position->x_position == -1 && position->y_position == -1) {
        position->x_position = 0;
        position->y_position = 0;
        return -1; // 数据无效
    }
    
    return 0;
}

/**
 * @brief 擦除位置数据
 * @retval 0:成功 -1:失败
 */
int Flash_Erase_Position(void)
{
    HAL_StatusTypeDef status;
    FLASH_EraseInitTypeDef erase_init;
    uint32_t sector_error = 0;
    
    // 解锁FLASH
    HAL_FLASH_Unlock();
    
    // 配置擦除参数
    erase_init.TypeErase = FLASH_TYPEERASE_SECTORS;
    erase_init.Sector = FLASH_SECTOR_11;
    erase_init.NbSectors = 1;
    erase_init.VoltageRange = FLASH_VOLTAGE_RANGE_3;
    
    // 擦除扇区
    status = HAL_FLASHEx_Erase(&erase_init, &sector_error);
    
    // 锁定FLASH
    HAL_FLASH_Lock();
    
    return (status == HAL_OK) ? 0 : -1;
}
