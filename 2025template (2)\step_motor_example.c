/**
 * @file step_motor_example.c
 * @brief 步进电机位置模式控制简单示例
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */

#include "step_motor_bsp.h"

/**
 * @brief 简单的步进电机位置控制示例
 * 
 * 使用方法：
 * 1. 在main函数中调用Step_Motor_Simple_Test()
 * 2. 上电后电机会自动执行预设的运动序列
 */

/**
 * @brief 步进电机简单测试函数
 * 上电时使能电机，分别转动一定的脉冲值，然后停止
 */
void Step_Motor_Simple_Test(void)
{
    // 等待系统初始化完成
    HAL_Delay(1000);
    
    // ========== 测试序列1 ==========
    // X轴正向转动2000个脉冲，Y轴负向转动1500个脉冲
    Step_Motor_Set_Pwm(2000, -1500);
    
    // 等待运动完成（根据30RPM速度估算约4秒）
    HAL_Delay(4000);
    
    // 停止所有电机
    Step_Motor_Stop();
    
    // 等待1秒
    HAL_Delay(1000);
    
    // ========== 测试序列2 ==========
    // X轴负向转动1000个脉冲，Y轴正向转动800个脉冲
    Step_Motor_Set_Pwm(-1000, 800);
    
    // 等待运动完成（约3秒）
    HAL_Delay(3000);
    
    // 停止所有电机
    Step_Motor_Stop();
    
    // 等待1秒
    HAL_Delay(1000);
    
    // ========== 测试序列3 ==========
    // 回到接近原点位置
    Step_Motor_Set_Pwm(1000, -800);  // 反向运动
    HAL_Delay(3000);
    
    Step_Motor_Set_Pwm(-2000, 1500); // 继续反向运动
    HAL_Delay(4000);
    
    // 最终停止
    Step_Motor_Stop();
}

/**
 * @brief 单轴测试函数
 * @param axis 轴选择：0=X轴，1=Y轴
 * @param pulses 脉冲数（正数为CW，负数为CCW）
 * @param delay_ms 运动后等待时间
 */
void Step_Motor_Single_Axis_Test(int axis, int pulses, int delay_ms)
{
    if (axis == 0) {
        // 只控制X轴
        Step_Motor_Set_Pwm(pulses, 0);
    } else {
        // 只控制Y轴
        Step_Motor_Set_Pwm(0, pulses);
    }
    
    // 等待运动完成
    if (delay_ms > 0) {
        HAL_Delay(delay_ms);
    }
    
    // 停止电机
    Step_Motor_Stop();
}

/**
 * @brief 方形轨迹测试
 * 让电机按照方形轨迹运动
 */
void Step_Motor_Square_Test(void)
{
    int side_pulses = 1000;  // 每边1000个脉冲
    int move_delay = 2000;   // 每段运动等待2秒
    
    HAL_Delay(1000); // 初始等待
    
    // 方形轨迹：右 -> 上 -> 左 -> 下
    
    // 向右移动
    Step_Motor_Set_Pwm(side_pulses, 0);
    HAL_Delay(move_delay);
    Step_Motor_Stop();
    HAL_Delay(500);
    
    // 向上移动
    Step_Motor_Set_Pwm(0, side_pulses);
    HAL_Delay(move_delay);
    Step_Motor_Stop();
    HAL_Delay(500);
    
    // 向左移动
    Step_Motor_Set_Pwm(-side_pulses, 0);
    HAL_Delay(move_delay);
    Step_Motor_Stop();
    HAL_Delay(500);
    
    // 向下移动
    Step_Motor_Set_Pwm(0, -side_pulses);
    HAL_Delay(move_delay);
    Step_Motor_Stop();
}

/**
 * @brief 对角线运动测试
 */
void Step_Motor_Diagonal_Test(void)
{
    HAL_Delay(1000);
    
    // 右上对角线
    Step_Motor_Set_Pwm(1500, 1500);
    HAL_Delay(3000);
    Step_Motor_Stop();
    HAL_Delay(1000);
    
    // 左下对角线（回到原点）
    Step_Motor_Set_Pwm(-1500, -1500);
    HAL_Delay(3000);
    Step_Motor_Stop();
    HAL_Delay(1000);
    
    // 左上对角线
    Step_Motor_Set_Pwm(-1000, 1000);
    HAL_Delay(2500);
    Step_Motor_Stop();
    HAL_Delay(1000);
    
    // 右下对角线（回到原点）
    Step_Motor_Set_Pwm(1000, -1000);
    HAL_Delay(2500);
    Step_Motor_Stop();
}

/* 
使用示例：

在main.c的main函数中，在Step_Motor_Init()之后调用：

// 基本测试
Step_Motor_Simple_Test();

// 或者单轴测试
Step_Motor_Single_Axis_Test(0, 1000, 2000);  // X轴正向1000脉冲
Step_Motor_Single_Axis_Test(1, -500, 1500);  // Y轴负向500脉冲

// 或者方形轨迹测试
Step_Motor_Square_Test();

// 或者对角线测试
Step_Motor_Diagonal_Test();

*/
