# 步进电机位置记录和回归功能说明

## 功能概述
实现了基于按键控制的步进电机位置记录和回归功能：
- **KEY1**：读取电机当前绝对位置并保存到FLASH
- **KEY2**：从FLASH读取保存的位置并控制电机回到该位置

## 硬件配置
- **按键1 (KEY1)**：PA0，按下时读取并保存当前位置
- **按键2 (KEY2)**：对应KEY2_Pin，按下时回到保存位置
- **FLASH存储**：使用扇区11 (0x080E0000) 存储位置数据
- **步进电机**：X轴(UART2)，Y轴(UART4)

## 主要文件说明

### 1. flash_bsp.c/h
FLASH存储操作模块：
```c
// 保存位置到FLASH
int Flash_Save_Position(Motor_Position_t *position);

// 从FLASH读取位置  
int Flash_Read_Position(Motor_Position_t *position);

// 擦除位置数据
int Flash_Erase_Position(void);
```

### 2. position_control.c/h
位置控制核心模块：
```c
// 初始化位置控制功能
void Position_Control_Init(void);

// 读取电机当前绝对位置
int Read_Motor_Current_Position(void);

// 保存当前位置到FLASH
int Save_Current_Position_To_Flash(void);

// 回到保存的位置
int Return_To_Saved_Position(void);

// 按键处理函数
void Position_Control_Key_Handler(int key);
```

### 3. key_bsp.c
按键处理模块（已更新）：
```c
void key_proc(void)
{
    // KEY1按下 - 读取并保存当前位置到FLASH
    if(key_down == 1) {
        Position_Control_Key_Handler(1);
    }
    
    // KEY2按下 - 从FLASH读取位置并回到该位置
    if(key_down == 2) {
        Position_Control_Key_Handler(2);
    }
}
```

## 使用流程

### 1. 系统初始化
```c
// 在main函数中
Step_Motor_Init();                    // 初始化步进电机
Position_Control_Init();              // 初始化位置控制功能
```

### 2. 保存位置操作
1. 手动移动电机到目标位置
2. 按下**KEY1**
3. 系统自动：
   - 发送位置读取命令到电机
   - 获取X轴和Y轴当前绝对位置
   - 将位置数据保存到FLASH扇区11
   - 串口输出确认信息

### 3. 回归位置操作
1. 按下**KEY2**
2. 系统自动：
   - 从FLASH读取保存的位置数据
   - 使用绝对位置模式控制电机移动
   - 等待运动完成
   - 串口输出确认信息

## 数据结构

### 位置数据结构
```c
typedef struct {
    int32_t x_position;  // X轴绝对位置
    int32_t y_position;  // Y轴绝对位置
} Motor_Position_t;
```

### 控制状态
```c
typedef enum {
    POS_CTRL_IDLE = 0,      // 空闲状态
    POS_CTRL_READING,       // 正在读取位置
    POS_CTRL_SAVING,        // 正在保存位置
    POS_CTRL_RETURNING,     // 正在回到位置
    POS_CTRL_ERROR          // 错误状态
} Position_Control_State_t;
```

## FLASH存储说明
- **存储地址**：0x080E0000 (扇区11起始地址)
- **存储大小**：8字节 (X轴4字节 + Y轴4字节)
- **数据格式**：32位有符号整数
- **擦除单位**：整个扇区 (128KB)

## 串口调试信息
系统通过UART1输出调试信息：
```
从FLASH读取保存位置: X=1000, Y=2000
KEY1按下 - 保存当前位置
位置已保存到FLASH: X=1000, Y=2000
KEY2按下 - 回到保存位置
开始回到保存位置: X=1000, Y=2000
已回到保存位置
```

## 注意事项

1. **位置读取**：当前实现中位置读取使用示例值，实际应用需要：
   - 实现UART接收中断
   - 解析EMM V5协议响应数据
   - 提取真实的位置信息

2. **运动时间估算**：系统根据位置距离自动估算运动时间
   - 基于30RPM转速计算
   - 最大等待时间限制为10秒

3. **错误处理**：
   - FLASH操作失败时会输出错误信息
   - 系统状态异常时自动恢复到空闲状态

4. **数据有效性**：
   - FLASH擦除后数据为0xFFFFFFFF
   - 系统会检查并处理无效数据

## 扩展功能建议

1. **多位置存储**：扩展为支持多个位置点的存储
2. **位置插值**：实现位置间的平滑插值运动
3. **位置校验**：添加位置数据的校验和机制
4. **运动状态反馈**：实时监控电机运动状态
5. **参数配置**：支持运动速度、加速度等参数配置

## 编译和使用
1. 确保所有新增文件已添加到工程中
2. 编译并下载到目标板
3. 连接串口调试工具查看运行状态
4. 使用按键测试位置记录和回归功能
