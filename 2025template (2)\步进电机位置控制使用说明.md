# 步进电机位置控制使用说明

## 概述
本代码实现了基于EMM V5步进电机驱动器的位置模式控制，通过UART通信控制双轴步进电机系统。

## 硬件配置
- X轴电机：UART2 (huart2)，地址0x01
- Y轴电机：UART4 (huart4)，地址0x01  
- 最大转速：30 RPM
- 加速度：0（直接启动）

## 上电自动测试代码

### 1. 基本实现（已集成到main.c）
```c
// 在main函数中的USER CODE BEGIN 2部分
Step_Motor_Init();                              // 初始化步进电机
Emm_V5_Reset_CurPos_To_Zero(&huart4, 0x01);    // 重置Y轴位置为零
Emm_V5_Reset_CurPos_To_Zero(&huart2, 0x01);    // 重置X轴位置为零
save_initial_position();                       // 保存初始位置

// 上电后自动运行位置控制测试
HAL_Delay(1000);                               // 等待1秒确保初始化完成

// 测试序列1：X轴正向转动2000个脉冲，Y轴负向转动1500个脉冲
Step_Motor_Set_Pwm(2000, -1500);
HAL_Delay(4000);                               // 等待运动完成
Step_Motor_Stop();

HAL_Delay(1000);                               // 等待1秒

// 测试序列2：X轴负向转动1000个脉冲，Y轴正向转动800个脉冲
Step_Motor_Set_Pwm(-1000, 800);
HAL_Delay(3000);                               // 等待运动完成
Step_Motor_Stop();
```

### 2. 扩展示例（step_motor_example.c）

#### 简单测试
```c
#include "step_motor_example.h"

// 在main函数中调用
Step_Motor_Simple_Test();  // 执行预设的运动序列
```

#### 单轴控制
```c
// X轴正向1000脉冲，等待2秒
Step_Motor_Single_Axis_Test(0, 1000, 2000);

// Y轴负向500脉冲，等待1.5秒  
Step_Motor_Single_Axis_Test(1, -500, 1500);
```

#### 方形轨迹
```c
Step_Motor_Square_Test();  // 按方形轨迹运动
```

#### 对角线运动
```c
Step_Motor_Diagonal_Test();  // 对角线运动测试
```

## 核心函数说明

### 1. Step_Motor_Set_Pwm()
```c
void Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance);
```
- **功能**：控制XY轴电机移动指定脉冲数（相对位置模式）
- **参数**：
  - x_distance：X轴移动脉冲数（正数=CW正转，负数=CCW反转）
  - y_distance：Y轴移动脉冲数（正数=CW正转，负数=CCW反转）

### 2. Emm_V5_Pos_Control()
```c
void Emm_V5_Pos_Control(UART_HandleTypeDef* huart, uint8_t addr, uint8_t dir, 
                        uint16_t vel, uint8_t acc, uint32_t clk, bool raF, bool snF);
```
- **功能**：EMM V5位置控制底层函数
- **参数**：
  - huart：串口句柄
  - addr：电机地址
  - dir：方向（0=CW，1=CCW）
  - vel：速度(RPM)，范围0-5000
  - acc：加速度，范围0-255（0=直接启动）
  - clk：脉冲数，范围0-(2^32-1)
  - raF：位置模式（false=相对运动，true=绝对位置）
  - snF：多机同步（false=不启用，true=启用）

### 3. Step_Motor_Stop()
```c
void Step_Motor_Stop(void);
```
- **功能**：立即停止所有电机

## 运动时间估算
基于30RPM的转速：
- 1000脉冲 ≈ 2秒
- 1500脉冲 ≈ 3秒  
- 2000脉冲 ≈ 4秒

## 使用注意事项
1. 上电后需等待至少1秒再发送运动命令
2. 每次运动后建议调用Step_Motor_Stop()确保停止
3. 连续运动之间建议有适当延时
4. 脉冲数范围：0 到 4,294,967,295
5. 正数脉冲为CW方向，负数脉冲为CCW方向

## 编译说明
1. 将step_motor_example.c和step_motor_example.h添加到工程中
2. 在需要使用的地方包含头文件：`#include "step_motor_example.h"`
3. 编译并下载到目标板

## 测试结果
上电后系统将自动执行以下动作：
1. 初始化电机并重置位置为零
2. X轴正向2000脉冲，Y轴负向1500脉冲
3. 停止并等待1秒
4. X轴负向1000脉冲，Y轴正向800脉冲  
5. 最终停止

整个测试过程约需8-10秒完成。
