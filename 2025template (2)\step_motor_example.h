/**
 * @file step_motor_example.h
 * @brief 步进电机位置模式控制简单示例头文件
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */

#ifndef __STEP_MOTOR_EXAMPLE_H__
#define __STEP_MOTOR_EXAMPLE_H__

#include "step_motor_bsp.h"

/* 函数声明 */

/**
 * @brief 步进电机简单测试函数
 * 上电时使能电机，分别转动一定的脉冲值，然后停止
 */
void Step_Motor_Simple_Test(void);

/**
 * @brief 单轴测试函数
 * @param axis 轴选择：0=X轴，1=Y轴
 * @param pulses 脉冲数（正数为CW，负数为CCW）
 * @param delay_ms 运动后等待时间
 */
void Step_Motor_Single_Axis_Test(int axis, int pulses, int delay_ms);

/**
 * @brief 方形轨迹测试
 * 让电机按照方形轨迹运动
 */
void Step_Motor_Square_Test(void);

/**
 * @brief 对角线运动测试
 */
void Step_Motor_Diagonal_Test(void);

#endif /* __STEP_MOTOR_EXAMPLE_H__ */
